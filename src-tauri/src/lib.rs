mod spotify;

use spotify::SpotifyState;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Load environment variables
    dotenv::dotenv().ok();

    tauri::Builder::default()
        .manage(SpotifyState::new())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            spotify::spotify_authenticate,
            spotify::spotify_get_playlists,
            spotify::spotify_get_playlist_tracks,
            spotify::spotify_get_access_token,
            spotify::spotify_start_playback,
            spotify::spotify_logout
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
