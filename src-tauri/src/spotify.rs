use base64::Engine;
use futures::future::FutureExt;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::State;
use warp::Filter;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SpotifyTokens {
    access_token: String,
    refresh_token: String,
    expires_in: u64,
    token_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyUser {
    id: String,
    display_name: Option<String>,
    email: Option<String>,
    images: Vec<SpotifyImage>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyImage {
    url: String,
    height: Option<u32>,
    width: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyPlaylist {
    id: String,
    name: String,
    description: Option<String>,
    images: Vec<SpotifyImage>,
    tracks: SpotifyPlaylistTracks,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyPlaylistTracks {
    total: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyTrack {
    id: String,
    name: String,
    artists: Vec<SpotifyArtist>,
    album: SpotifyAlbum,
    duration_ms: u32,
    preview_url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyArtist {
    id: String,
    name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyAlbum {
    id: String,
    name: String,
    images: Vec<SpotifyImage>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResult {
    success: bool,
    user: Option<SpotifyUser>,
    error: Option<String>,
}

pub struct SpotifyState {
    pub tokens: Mutex<Option<SpotifyTokens>>,
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
}

impl SpotifyState {
    pub fn new() -> Self {
        Self {
            tokens: Mutex::new(None),
            client_id: std::env::var("SPOTIFY_CLIENT_ID").unwrap_or_default(),
            client_secret: std::env::var("SPOTIFY_CLIENT_SECRET").unwrap_or_default(),
            redirect_uri: "http://127.0.0.1:8888/callback".to_string(),
        }
    }
}

#[tauri::command]
pub async fn spotify_authenticate(
    state: State<'_, SpotifyState>,
    app_handle: tauri::AppHandle,
) -> Result<AuthResult, String> {
    let client_id = &state.client_id;
    let redirect_uri = &state.redirect_uri;

    if client_id.is_empty() {
        return Ok(AuthResult {
            success: false,
            user: None,
            error: Some("Missing Spotify credentials".to_string()),
        });
    }

    // Create authorization URL
    let scopes = vec![
        "user-read-private",
        "user-read-email",
        "playlist-read-private",
        "playlist-read-collaborative",
        "streaming",
        "user-read-playback-state",
        "user-modify-playback-state",
    ];

    let auth_url = format!(
        "https://accounts.spotify.com/authorize?response_type=code&client_id={}&scope={}&redirect_uri={}&state={}",
        client_id,
        scopes.join("%20"),
        urlencoding::encode(redirect_uri),
        uuid::Uuid::new_v4()
    );

    // Open authorization URL in browser
    if let Err(e) =
        tauri_plugin_opener::OpenerExt::opener(&app_handle).open_url(auth_url, None::<String>)
    {
        return Ok(AuthResult {
            success: false,
            user: None,
            error: Some(format!("Failed to open browser: {}", e)),
        });
    }

    // Start callback server and wait for authorization code
    match start_callback_server(&state).await {
        Ok(user) => Ok(AuthResult {
            success: true,
            user: Some(user),
            error: None,
        }),
        Err(e) => Ok(AuthResult {
            success: false,
            user: None,
            error: Some(e),
        }),
    }
}

async fn start_callback_server(state: &SpotifyState) -> Result<SpotifyUser, String> {
    let (tx, rx) = tokio::sync::oneshot::channel::<Result<String, String>>();
    let tx = Arc::new(Mutex::new(Some(tx)));

    // Create callback route
    let callback = warp::path("callback")
        .and(warp::query::<HashMap<String, String>>())
        .and(warp::any().map(move || tx.clone()))
        .map(|params: HashMap<String, String>, tx: Arc<Mutex<Option<tokio::sync::oneshot::Sender<Result<String, String>>>>>| {
            let response = if let Some(code) = params.get("code") {
                // Send the authorization code through the channel
                if let Ok(mut sender) = tx.lock() {
                    if let Some(sender) = sender.take() {
                        let _ = sender.send(Ok(code.clone()));
                    }
                }

                warp::reply::html(
                    r#"
                    <html>
                        <head><title>Spotify Authentication</title></head>
                        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                            <h1 style="color: #1db954;">Authentication Successful!</h1>
                            <p>You can now close this window and return to the app.</p>
                            <script>
                                setTimeout(() => {
                                    window.close();
                                }, 2000);
                            </script>
                        </body>
                    </html>
                    "#
                )
            } else if let Some(error) = params.get("error") {
                // Send the error through the channel
                if let Ok(mut sender) = tx.lock() {
                    if let Some(sender) = sender.take() {
                        let _ = sender.send(Err(error.clone()));
                    }
                }

                warp::reply::html(
                    r#"
                    <html>
                        <head><title>Spotify Authentication Error</title></head>
                        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                            <h1 style="color: #e22134;">Authentication Failed</h1>
                            <p>There was an error during authentication. Please try again.</p>
                            <script>
                                setTimeout(() => {
                                    window.close();
                                }, 3000);
                            </script>
                        </body>
                    </html>
                    "#
                )
            } else {
                warp::reply::html(
                    r#"
                    <html>
                        <head><title>Spotify Authentication</title></head>
                        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                            <h1>Invalid Request</h1>
                            <p>Missing authorization code or error parameter.</p>
                        </body>
                    </html>
                    "#
                )
            };

            response
        });

    let routes = callback;

    // Start the server
    let server = warp::serve(routes).bind(([127, 0, 0, 1], 8888)).fuse();

    // Wait for either the callback or a timeout
    let timeout = tokio::time::sleep(tokio::time::Duration::from_secs(120)).fuse();

    tokio::select! {
        _ = server => {
            Err("Server stopped unexpectedly".to_string())
        }
        _ = timeout => {
            Err("Authentication timeout".to_string())
        }
        result = rx => {
            match result {
                Ok(Ok(code)) => {
                    // Exchange authorization code for tokens
                    exchange_code_for_tokens(state, &code).await
                }
                Ok(Err(error)) => {
                    Err(format!("OAuth error: {}", error))
                }
                Err(_) => {
                    Err("Failed to receive callback".to_string())
                }
            }
        }
    }
}

async fn exchange_code_for_tokens(state: &SpotifyState, code: &str) -> Result<SpotifyUser, String> {
    let client = reqwest::Client::new();

    // Prepare token exchange request
    let params = [
        ("grant_type", "authorization_code"),
        ("code", code),
        ("redirect_uri", &state.redirect_uri),
    ];

    // Create basic auth header
    let auth_string = format!("{}:{}", state.client_id, state.client_secret);
    let auth_header = format!(
        "Basic {}",
        base64::prelude::BASE64_STANDARD.encode(auth_string)
    );

    // Exchange code for tokens
    let response = client
        .post("https://accounts.spotify.com/api/token")
        .header("Authorization", auth_header)
        .header("Content-Type", "application/x-www-form-urlencoded")
        .form(&params)
        .send()
        .await
        .map_err(|e| format!("Token exchange request failed: {}", e))?;

    let status = response.status();
    if !status.is_success() {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        return Err(format!(
            "Token exchange failed: {} - {}",
            status, error_text
        ));
    }

    let token_response: SpotifyTokens = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse token response: {}", e))?;

    // Store tokens
    {
        let mut tokens = state.tokens.lock().unwrap();
        *tokens = Some(token_response.clone());
    }

    // Get user profile
    get_current_user(&token_response.access_token).await
}

async fn get_current_user(access_token: &str) -> Result<SpotifyUser, String> {
    let client = reqwest::Client::new();
    let response = client
        .get("https://api.spotify.com/v1/me")
        .header("Authorization", format!("Bearer {}", access_token))
        .send()
        .await
        .map_err(|e| format!("User profile request failed: {}", e))?;

    let status = response.status();
    if status.is_success() {
        let user: SpotifyUser = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse user profile: {}", e))?;
        Ok(user)
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        Err(format!(
            "Failed to get user profile: {} - {}",
            status, error_text
        ))
    }
}

#[tauri::command]
pub async fn spotify_get_playlists(
    state: State<'_, SpotifyState>,
) -> Result<Vec<SpotifyPlaylist>, String> {
    let access_token = {
        let tokens = state.tokens.lock().unwrap();
        if let Some(tokens) = tokens.as_ref() {
            tokens.access_token.clone()
        } else {
            return Err("Not authenticated".to_string());
        }
    };

    // Make API call to get playlists
    get_user_playlists(&access_token).await
}

async fn get_user_playlists(access_token: &str) -> Result<Vec<SpotifyPlaylist>, String> {
    let client = reqwest::Client::new();
    let response = client
        .get("https://api.spotify.com/v1/me/playlists")
        .header("Authorization", format!("Bearer {}", access_token))
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = response.status();
    if status.is_success() {
        let json: serde_json::Value = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse JSON: {}", e))?;

        // Parse playlists from Spotify API response
        let playlists = json["items"]
            .as_array()
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|item| {
                Some(SpotifyPlaylist {
                    id: item["id"].as_str()?.to_string(),
                    name: item["name"].as_str()?.to_string(),
                    description: item["description"].as_str().map(|s| s.to_string()),
                    images: item["images"]
                        .as_array()
                        .unwrap_or(&vec![])
                        .iter()
                        .filter_map(|img| {
                            Some(SpotifyImage {
                                url: img["url"].as_str()?.to_string(),
                                height: img["height"].as_u64().map(|h| h as u32),
                                width: img["width"].as_u64().map(|w| w as u32),
                            })
                        })
                        .collect(),
                    tracks: SpotifyPlaylistTracks {
                        total: item["tracks"]["total"].as_u64().unwrap_or(0) as u32,
                    },
                })
            })
            .collect();

        Ok(playlists)
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        Err(format!("API request failed: {} - {}", status, error_text))
    }
}

#[tauri::command]
pub async fn spotify_get_access_token(state: State<'_, SpotifyState>) -> Result<String, String> {
    let tokens = state.tokens.lock().unwrap();
    if let Some(tokens) = tokens.as_ref() {
        Ok(tokens.access_token.clone())
    } else {
        Err("Not authenticated".to_string())
    }
}

#[tauri::command]
pub async fn spotify_logout(state: State<'_, SpotifyState>) -> Result<(), String> {
    let mut tokens = state.tokens.lock().unwrap();
    *tokens = None;
    Ok(())
}

#[tauri::command]
pub async fn spotify_get_playlist_tracks(
    state: State<'_, SpotifyState>,
    playlist_id: String,
) -> Result<Vec<SpotifyTrack>, String> {
    let access_token = {
        let tokens = state.tokens.lock().unwrap();
        if let Some(tokens) = tokens.as_ref() {
            tokens.access_token.clone()
        } else {
            return Err("Not authenticated".to_string());
        }
    };

    get_playlist_tracks(&access_token, &playlist_id).await
}

async fn get_playlist_tracks(
    access_token: &str,
    playlist_id: &str,
) -> Result<Vec<SpotifyTrack>, String> {
    let client = reqwest::Client::new();
    let url = format!(
        "https://api.spotify.com/v1/playlists/{}/tracks",
        playlist_id
    );

    let response = client
        .get(&url)
        .header("Authorization", format!("Bearer {}", access_token))
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = response.status();
    if status.is_success() {
        let json: serde_json::Value = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse JSON: {}", e))?;

        // Parse tracks from Spotify API response
        let tracks = json["items"]
            .as_array()
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|item| {
                let track = &item["track"];
                Some(SpotifyTrack {
                    id: track["id"].as_str()?.to_string(),
                    name: track["name"].as_str()?.to_string(),
                    artists: track["artists"]
                        .as_array()
                        .unwrap_or(&vec![])
                        .iter()
                        .filter_map(|artist| {
                            Some(SpotifyArtist {
                                id: artist["id"].as_str()?.to_string(),
                                name: artist["name"].as_str()?.to_string(),
                            })
                        })
                        .collect(),
                    album: SpotifyAlbum {
                        id: track["album"]["id"].as_str()?.to_string(),
                        name: track["album"]["name"].as_str()?.to_string(),
                        images: track["album"]["images"]
                            .as_array()
                            .unwrap_or(&vec![])
                            .iter()
                            .filter_map(|img| {
                                Some(SpotifyImage {
                                    url: img["url"].as_str()?.to_string(),
                                    height: img["height"].as_u64().map(|h| h as u32),
                                    width: img["width"].as_u64().map(|w| w as u32),
                                })
                            })
                            .collect(),
                    },
                    duration_ms: track["duration_ms"].as_u64().unwrap_or(0) as u32,
                    preview_url: track["preview_url"].as_str().map(|s| s.to_string()),
                })
            })
            .collect();

        Ok(tracks)
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        Err(format!("API request failed: {} - {}", status, error_text))
    }
}

#[tauri::command]
pub async fn spotify_start_playback(
    state: State<'_, SpotifyState>,
    device_id: String,
    body: serde_json::Value,
) -> Result<(), String> {
    let access_token = {
        let tokens = state.tokens.lock().unwrap();
        if let Some(tokens) = tokens.as_ref() {
            tokens.access_token.clone()
        } else {
            return Err("Not authenticated".to_string());
        }
    };

    start_playback(&access_token, &device_id, body).await
}

async fn start_playback(
    access_token: &str,
    device_id: &str,
    body: serde_json::Value,
) -> Result<(), String> {
    let client = reqwest::Client::new();
    let url = format!(
        "https://api.spotify.com/v1/me/player/play?device_id={}",
        device_id
    );

    let response = client
        .put(&url)
        .header("Authorization", format!("Bearer {}", access_token))
        .header("Content-Type", "application/json")
        .json(&body)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = response.status();
    if status.is_success() || status.as_u16() == 204 {
        Ok(())
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        Err(format!(
            "Playback request failed: {} - {}",
            status, error_text
        ))
    }
}
