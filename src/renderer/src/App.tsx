import { useState } from 'react'
import { Music, Download, Settings, LogIn } from 'lucide-react'
import { useSpotify } from './hooks/useSpotify'
// import { PlaylistView } from './components/PlaylistView'
// import { Player } from './components/Player'
// import { PreviewPlayer } from './components/PreviewPlayer'

function App(): JSX.Element {
  const [activeTab, setActiveTab] = useState('playlists')
  const {
    isAuthenticated,
    user,
    // playlists,
    // selectedPlaylist,
    // tracks,
    loading,
    error,
    // player,
    authenticate,
    logout,
    // selectPlaylist,
    // playTrack,
    setError
  } = useSpotify()

  /*
  const {
    isAuthenticated,
    user,
    playlists,
    selectedPlaylist,
    tracks,
    loading,
    error,
    player,
    authenticate,
    logout,
    selectPlaylist,
    playTrack,
    setError
  } = useSpotify()
  */

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-400 to-blue-600 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
              <Music className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Spotify-Soulseek App (Tauri)</h1>
            <p className="text-gray-600 mb-6">
              Connect your Spotify account to test the OAuth flow with our new Rust backend
            </p>
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}
            <button
              onClick={authenticate}
              disabled={loading}
              className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center disabled:opacity-50"
            >
              <LogIn className="w-4 h-4 mr-2" />
              {loading ? 'Connecting...' : 'Connect with Spotify'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Show authenticated state if logged in
  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center bg-white p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold text-green-600 mb-4">🎉 Authentication Successful!</h1>
          <p className="text-gray-600 mb-4">Welcome, {user?.display_name || 'Spotify User'}!</p>
          <p className="text-sm text-gray-500 mb-6">
            The OAuth flow with our Rust callback server worked perfectly.
          </p>
          <button
            onClick={logout}
            className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md"
          >
            Logout
          </button>
        </div>
      </div>
    )
  }

  /*
  // Full UI - commented out for testing
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      // ... rest of the UI
    </div>
  )
  */
}

export default App
