import { tauriSpotifyService } from './TauriSpotifyService'

// Spotify Web Playback SDK types
declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void;
    Spotify: {
      Player: new (options: {
        name: string;
        getOAuthToken: (cb: (token: string) => void) => void;
        volume?: number;
      }) => SpotifyPlayer;
    };
  }
}

interface SpotifyPlayer {
  addListener(event: string, callback: (data: any) => void): boolean;
  removeListener(event: string, callback?: (data: any) => void): boolean;
  connect(): Promise<boolean>;
  disconnect(): void;
  getCurrentState(): Promise<SpotifyPlayerState | null>;
  getVolume(): Promise<number>;
  nextTrack(): Promise<void>;
  pause(): Promise<void>;
  previousTrack(): Promise<void>;
  resume(): Promise<void>;
  seek(position_ms: number): Promise<void>;
  setName(name: string): Promise<void>;
  setVolume(volume: number): Promise<void>;
  togglePlay(): Promise<void>;
}

interface SpotifyPlayerState {
  context: {
    uri: string;
    metadata: any;
  };
  disallows: {
    pausing: boolean;
    peeking_next: boolean;
    peeking_prev: boolean;
    resuming: boolean;
    seeking: boolean;
    skipping_next: boolean;
    skipping_prev: boolean;
  };
  paused: boolean;
  position: number;
  repeat_mode: number;
  shuffle: boolean;
  track_window: {
    current_track: SpotifyTrack;
    next_tracks: SpotifyTrack[];
    previous_tracks: SpotifyTrack[];
  };
}

interface SpotifyTrack {
  id: string;
  uri: string;
  name: string;
  artists: Array<{ name: string; uri: string }>;
  album: {
    name: string;
    uri: string;
    images: Array<{ url: string; height: number; width: number }>;
  };
  duration_ms: number;
}

export class SpotifyPlayerService {
  private player: SpotifyPlayer | null = null;
  private deviceId: string | null = null;
  private isReady = false;
  private accessToken: string | null = null;
  private listeners: Map<string, Set<Function>> = new Map();
  private initializationFailed = false;

  constructor() {
    this.initializeSDK();
  }

  private initializeSDK(): void {
    if (window.Spotify) {
      this.createPlayer();
    } else {
      window.onSpotifyWebPlaybackSDKReady = () => {
        this.createPlayer();
      };
    }
  }

  private createPlayer(): void {
    if (!window.Spotify || this.player || this.initializationFailed) return;

    try {
      this.player = new window.Spotify.Player({
        name: 'Spotify-Soulseek App',
        getOAuthToken: (cb) => {
          if (this.accessToken) {
            cb(this.accessToken);
          } else {
            // Get token from Tauri backend
            tauriSpotifyService.getAccessToken().then((token: string) => {
              this.accessToken = token;
              cb(token);
            }).catch((error) => {
              console.error('Failed to get access token:', error);
              this.emit('authentication_error', { message: 'Failed to get access token' });
            });
          }
        },
        volume: 0.5
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to create Spotify player:', error);
      this.initializationFailed = true;
      this.emit('initialization_error', {
        message: 'Web Playback SDK not supported in this environment. Music preview functionality will be limited.'
      });
    }
  }

  private setupEventListeners(): void {
    if (!this.player) return;

    // Ready
    this.player.addListener('ready', ({ device_id }) => {
      console.log('Ready with Device ID', device_id);
      this.deviceId = device_id;
      this.isReady = true;
      this.emit('ready', { device_id });
    });

    // Not Ready
    this.player.addListener('not_ready', ({ device_id }) => {
      console.log('Device ID has gone offline', device_id);
      this.isReady = false;
      this.emit('not_ready', { device_id });
    });

    // Player state changed
    this.player.addListener('player_state_changed', (state) => {
      console.log('Player state changed', state);
      this.emit('player_state_changed', state);
    });

    // Initialization Error
    this.player.addListener('initialization_error', ({ message }) => {
      console.error('Failed to initialize', message);
      this.initializationFailed = true;
      this.emit('initialization_error', {
        message: 'Web Playback SDK initialization failed. This may be due to DRM restrictions in Electron. Music preview functionality will be limited.'
      });
    });

    // Authentication Error
    this.player.addListener('authentication_error', ({ message }) => {
      console.error('Failed to authenticate', message);
      this.emit('authentication_error', { message });
    });

    // Account Error
    this.player.addListener('account_error', ({ message }) => {
      console.error('Failed to validate Spotify account', message);
      this.emit('account_error', { message });
    });

    // Playback Error
    this.player.addListener('playback_error', ({ message }) => {
      console.error('Failed to perform playback', message);
      this.emit('playback_error', { message });
    });
  }

  async connect(): Promise<boolean> {
    if (this.initializationFailed) {
      console.warn('Cannot connect: Web Playback SDK initialization failed');
      return false;
    }
    if (!this.player) {
      console.warn('Player not initialized, cannot connect');
      return false;
    }
    try {
      const result = await this.player.connect();
      if (!result) {
        console.warn('Player connection returned false');
        this.initializationFailed = true;
      }
      return result;
    } catch (error) {
      console.warn('Player connection failed:', error);
      this.initializationFailed = true;
      return false;
    }
  }

  disconnect(): void {
    if (this.player) {
      this.player.disconnect();
    }
  }

  async play(uris?: string[], contextUri?: string): Promise<void> {
    if (this.initializationFailed || !this.deviceId) {
      // Fallback: Try to play preview URL if available
      if (uris && uris.length > 0) {
        const trackId = uris[0].replace('spotify:track:', '');
        throw new Error(`Web Playback SDK not available. Track ID: ${trackId}. Please use Spotify app or web player for full playback.`);
      }
      throw new Error('Device not ready and Web Playback SDK not available');
    }

    const body: any = {};
    if (uris) {
      body.uris = uris;
    }
    if (contextUri) {
      body.context_uri = contextUri;
    }

    // Use Spotify Web API to start playback on our device
    await tauriSpotifyService.startPlayback(this.deviceId, body);
  }

  async pause(): Promise<void> {
    if (this.player) {
      await this.player.pause();
    }
  }

  async resume(): Promise<void> {
    if (this.player) {
      await this.player.resume();
    }
  }

  async togglePlay(): Promise<void> {
    if (this.player) {
      await this.player.togglePlay();
    }
  }

  async seek(position: number): Promise<void> {
    if (this.player) {
      await this.player.seek(position);
    }
  }

  async setVolume(volume: number): Promise<void> {
    if (this.player) {
      await this.player.setVolume(volume);
    }
  }

  async nextTrack(): Promise<void> {
    if (this.player) {
      await this.player.nextTrack();
    }
  }

  async previousTrack(): Promise<void> {
    if (this.player) {
      await this.player.previousTrack();
    }
  }

  async getCurrentState(): Promise<SpotifyPlayerState | null> {
    if (this.player) {
      return await this.player.getCurrentState();
    }
    return null;
  }

  setAccessToken(token: string): void {
    this.accessToken = token;
  }

  getDeviceId(): string | null {
    return this.deviceId;
  }

  isPlayerReady(): boolean {
    return this.isReady;
  }

  // Event system
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  off(event: string, callback?: Function): void {
    if (callback) {
      this.listeners.get(event)?.delete(callback);
    } else {
      this.listeners.delete(event);
    }
  }

  private emit(event: string, data: any): void {
    this.listeners.get(event)?.forEach(callback => callback(data));
  }
}

// Singleton instance
export const spotifyPlayerService = new SpotifyPlayerService();
