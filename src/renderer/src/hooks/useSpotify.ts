import { useState, useEffect } from 'react'
import { useSpotifyPlayer } from './useSpotifyPlayer'
import { previewPlayerService } from '../services/PreviewPlayerService'
import { tauriSpotifyService, SpotifyUser, SpotifyPlaylist } from '../services/TauriSpotifyService'

interface SpotifyTrack {
  id: string
  name: string
  artists: Array<{ id: string; name: string }>
  album: {
    id: string
    name: string
    images: Array<{ url: string }>
  }
  duration_ms: number
  preview_url?: string
}

export function useSpotify() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<SpotifyUser | null>(null)
  const [playlists, setPlaylists] = useState<SpotifyPlaylist[]>([])
  const [selectedPlaylist, setSelectedPlaylist] = useState<SpotifyPlaylist | null>(null)
  const [tracks, setTracks] = useState<SpotifyTrack[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize player
  const player = useSpotifyPlayer()



  const authenticate = async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await tauriSpotifyService.authenticate()
      if (result.success) {
        setIsAuthenticated(true)
        setUser(result.user!)

        // Set up player with access token (non-blocking)
        setupPlayer()

        // Automatically load playlists after authentication
        await loadPlaylists()
      } else {
        setError(result.error || 'Authentication failed')
      }
    } catch (error) {
      console.error('Authentication error:', error)
      setError('Authentication failed')
    } finally {
      setLoading(false)
    }
  }

  const setupPlayer = async () => {
    try {
      const accessToken = await tauriSpotifyService.getAccessToken()
      player.setAccessToken(accessToken)

      // Try to connect, but don't block if it fails
      const connected = await player.connect()
      if (!connected) {
        console.log('Web Playback SDK connection failed, using preview fallback')
      }
    } catch (playerError) {
      console.log('Player setup failed, using preview fallback:', playerError)
      // Player will fall back to preview mode
    }
  }

  const logout = async () => {
    try {
      // Disconnect player and stop preview playback
      player.disconnect()
      previewPlayerService.stop()

      await tauriSpotifyService.logout()
      setIsAuthenticated(false)
      setUser(null)
      setPlaylists([])
      setSelectedPlaylist(null)
      setTracks([])
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const loadPlaylists = async () => {
    setLoading(true)
    setError(null)
    try {
      const playlistData = await tauriSpotifyService.getPlaylists()
      setPlaylists(playlistData)
    } catch (error) {
      console.error('Error loading playlists:', error)
      setError('Failed to load playlists')
    } finally {
      setLoading(false)
    }
  }

  const loadPlaylistTracks = async (playlistId: string) => {
    setLoading(true)
    setError(null)
    try {
      const trackData = await tauriSpotifyService.getPlaylistTracks(playlistId)
      setTracks(trackData)
    } catch (error) {
      console.error('Error loading tracks:', error)
      setError('Failed to load tracks')
    } finally {
      setLoading(false)
    }
  }

  const selectPlaylist = async (playlist: SpotifyPlaylist) => {
    setSelectedPlaylist(playlist)
    await loadPlaylistTracks(playlist.id)
  }

  const playTrack = async (trackUri: string) => {
    try {
      // Try Web Playback SDK first
      if (player.isConnected && player.playerState.isReady) {
        await player.play([trackUri])
        setError(null)
        return
      }

      // Fallback to preview player
      const trackId = trackUri.replace('spotify:track:', '')
      const track = tracks.find(t => t.id === trackId)

      if (track?.preview_url) {
        await previewPlayerService.playPreview(track)
        setError(null)
      } else {
        setError('No preview available for this track. Please use the Spotify app for full playback.')
      }
    } catch (error) {
      console.error('Error playing track:', error)
      if (error instanceof Error && error.message.includes('No preview URL')) {
        setError('No preview available for this track')
      } else {
        setError('Failed to play track')
      }
    }
  }

  return {
    // State
    isAuthenticated,
    user,
    playlists,
    selectedPlaylist,
    tracks,
    loading,
    error,

    // Player state
    player,

    // Actions
    authenticate,
    logout,
    loadPlaylists,
    selectPlaylist,
    playTrack,
    setError
  }
}
